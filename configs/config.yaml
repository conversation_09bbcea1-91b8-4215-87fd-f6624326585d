service:
  name: "core-service"
  version: "1.0.0"
  environment: "development"

server:
  port: 8080
  host: "0.0.0.0"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"

database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "postgres"
  database: "torra_dev"
  ssl_mode: "disable"
  max_open_conns: 25
  max_idle_conns: 25
  conn_max_lifetime: "5m"

redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"

rabbitmq:
  url: "amqp://guest:guest@localhost:5672/"
  exchange: "torra.events"
  queue: "core.events"
  consumer_tag: "core-service"
  prefetch_count: 1
  reconnect_delay: "5s"

jwt:
  secret: "your-super-secret-jwt-key-change-this-in-production"
  access_duration: "15m"
  refresh_duration: "7d"
  issuer: "torra-core-service"

aws:
  region: "us-east-1"
  access_key_id: ""
  secret_access_key: ""
  s3_bucket: ""
