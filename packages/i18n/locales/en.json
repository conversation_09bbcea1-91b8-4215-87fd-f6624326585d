{"auth.welcome": "Welcome to MyTorra", "auth.login_success": "Login successful", "auth.logout_success": "Logout successful", "auth.registration_success": "Registration successful! Please verify your email.", "auth.invalid_credentials": "Invalid email or password", "auth.account_suspended": "Your account has been suspended", "auth.account_not_verified": "Please verify your email address", "auth.mfa_required": "Two-factor authentication required", "auth.mfa_invalid": "Invalid verification code", "auth.password_reset_sent": "Password reset instructions sent to your email", "auth.password_reset_success": "Password reset successful", "auth.email_verified": "Email verified successfully", "auth.phone_verified": "Phone number verified successfully", "auth.invalid_password": "Invalid password", "user.not_found": "User not found", "user.already_exists": "User with this email already exists", "user.profile_updated": "Profile updated successfully", "user.area_updated": "Area updated successfully", "user.verification_updated": "Verification status updated", "area.not_found": "Area not found", "area.not_active": "Selected area is not active", "area.invalid": "Invalid area selected", "area.service_unavailable": "Service not available in this area", "payment.success": "Payment processed successfully", "payment.failed": "Payment failed", "payment.insufficient_funds": "Insufficient funds", "payment.method_not_supported": "Payment method not supported", "payment.refund_success": "Refund processed successfully", "payment.refund_failed": "Refund failed", "subscription.created": "Subscription created successfully", "subscription.upgraded": "Subscription upgraded successfully", "subscription.downgraded": "Subscription downgraded successfully", "subscription.cancelled": "Subscription cancelled", "subscription.paused": "Subscription paused", "subscription.resumed": "Subscription resumed", "subscription.expired": "Subscription expired", "subscription.limit_reached": "Usage limit reached for %s", "notification.sent": "Notification sent successfully", "notification.preferences_updated": "Notification preferences updated", "validation.required": "This field is required", "validation.email": "Please enter a valid email address", "validation.phone": "Please enter a valid phone number (E.164 format)", "validation.password.min": "Password must be at least 12 characters", "validation.password.uppercase": "Password must contain at least one uppercase letter", "validation.password.lowercase": "Password must contain at least one lowercase letter", "validation.password.number": "Password must contain at least one number", "validation.password.special": "Password must contain at least one special character", "validation.min_length": "Must be at least %d characters", "validation.max_length": "Must be no more than %d characters", "validation.area_invalid": "Invalid area selected", "validation.terms_required": "You must accept the terms and conditions", "validation.password_mismatch": "Passwords do not match", "validation.invalid_input": "Invalid input provided", "validation.invalid_code": "Invalid verification code", "validation.password_or_mfa_required": "Either password or MFA code is required", "mfa.already_enabled": "Two-factor authentication is already enabled", "mfa.not_enabled": "Two-factor authentication is not enabled", "mfa.invalid_code": "Invalid two-factor authentication code", "verification.already_verified": "Already verified", "error.internal": "An internal error occurred. Please try again later.", "error.unauthorized": "You are not authorized to perform this action", "error.forbidden": "Access forbidden", "error.not_found": "Resource not found", "error.bad_request": "Invalid request", "error.rate_limit": "Too many requests. Please try again later.", "error.service_unavailable": "Service temporarily unavailable"}