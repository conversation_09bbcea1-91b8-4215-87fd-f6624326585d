# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# Development files
.env*
!.env.example
.vscode/
.idea/
*.swp
*.swo
*~

# Build artifacts
bin/
dist/
build/
target/

# Test files
*_test.go
testdata/
coverage.txt
coverage.html
*.test

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
.tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
*.sublime-project
*.sublime-workspace

# Dependency directories
vendor/
node_modules/

# Go specific
*.exe
*.exe~
*.dll
*.so
*.dylib

# Docker
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
Jenkinsfile

# Local development
docker-compose.override.yml
.air.toml
