# Torra Platform Environment Configuration Template
# Copy this file to .env and update with your values

# Environment
MYTORRA_ENVIRONMENT=development
MYTORRA_SERVICE_NAME=core
MYTORRA_LOG_LEVEL=debug

# Server Configuration
MYTORRA_SERVER_PORT=8080
MYTORRA_SERVER_HOST=0.0.0.0
MYTORRA_SERVER_READ_TIMEOUT=30s
MYTORRA_SERVER_WRITE_TIMEOUT=30s
MYTORRA_SERVER_IDLE_TIMEOUT=120s

# Database Configuration
MYTORRA_DATABASE_HOST=localhost
MYTORRA_DATABASE_PORT=5432
MYTORRA_DATABASE_USER=torra
MYTORRA_DATABASE_PASSWORD=torra_dev_password
MYTORRA_DATABASE_NAME=torra_dev
MYTORRA_DATABASE_SSL_MODE=disable
MYTORRA_DATABASE_MAX_CONNECTIONS=25
MYTORRA_DATABASE_MAX_IDLE_CONNECTIONS=5
MYTORRA_DATABASE_CONNECTION_MAX_LIFETIME=30m

# Redis Configuration
MYTORRA_REDIS_HOST=localhost
MYTORRA_REDIS_PORT=6379
MYTORRA_REDIS_PASSWORD=
MYTORRA_REDIS_DB=0
MYTORRA_REDIS_POOL_SIZE=10

# RabbitMQ Configuration
MYTORRA_RABBITMQ_HOST=localhost
MYTORRA_RABBITMQ_PORT=5672
MYTORRA_RABBITMQ_USER=torra
MYTORRA_RABBITMQ_PASSWORD=torra_dev_password
MYTORRA_RABBITMQ_VHOST=torra

# Meilisearch Configuration
MYTORRA_MEILISEARCH_HOST=http://localhost:7700
MYTORRA_MEILISEARCH_API_KEY=torra_dev_master_key

# Centrifugo Configuration
MYTORRA_CENTRIFUGO_URL=http://localhost:8000
MYTORRA_CENTRIFUGO_API_URL=http://localhost:8001
MYTORRA_CENTRIFUGO_API_KEY=torra-api-key-dev
MYTORRA_CENTRIFUGO_SECRET=torra-centrifugo-secret-key-dev

# MinIO Configuration (S3-compatible storage)
MYTORRA_MINIO_ENDPOINT=localhost:9000
MYTORRA_MINIO_ACCESS_KEY=torra_minio
MYTORRA_MINIO_SECRET_KEY=torra_dev_password
MYTORRA_MINIO_USE_SSL=false
MYTORRA_MINIO_BUCKET_NAME=torra-media

# JWT Configuration
MYTORRA_JWT_SECRET=your-super-secret-jwt-key-change-in-production
MYTORRA_JWT_ACCESS_TOKEN_DURATION=15m
MYTORRA_JWT_REFRESH_TOKEN_DURATION=7d

# Email Configuration (Mailhog for development)
MYTORRA_SMTP_HOST=localhost
MYTORRA_SMTP_PORT=1025
MYTORRA_SMTP_USER=
MYTORRA_SMTP_PASSWORD=
MYTORRA_SMTP_FROM_EMAIL=<EMAIL>
MYTORRA_SMTP_FROM_NAME=Torra Platform

# Payment Gateway Configuration
MYTORRA_PAYMENT_GATEWAY=stripe
MYTORRA_STRIPE_API_KEY=sk_test_xxxxx
MYTORRA_STRIPE_WEBHOOK_SECRET=whsec_xxxxx

# OAuth2 Configuration
MYTORRA_OAUTH_GOOGLE_CLIENT_ID=
MYTORRA_OAUTH_GOOGLE_CLIENT_SECRET=
MYTORRA_OAUTH_FACEBOOK_CLIENT_ID=
MYTORRA_OAUTH_FACEBOOK_CLIENT_SECRET=

# Rate Limiting
MYTORRA_RATE_LIMIT_REQUESTS_PER_SECOND=10
MYTORRA_RATE_LIMIT_BURST=20

# CORS Configuration
MYTORRA_CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
MYTORRA_CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
MYTORRA_CORS_ALLOWED_HEADERS=Content-Type,Authorization
MYTORRA_CORS_MAX_AGE=3600

# Feature Flags
MYTORRA_FEATURE_ENABLE_SIGNUP=true
MYTORRA_FEATURE_ENABLE_OAUTH=true
MYTORRA_FEATURE_ENABLE_MFA=false
MYTORRA_FEATURE_MAINTENANCE_MODE=false

# Monitoring
MYTORRA_METRICS_ENABLED=true
MYTORRA_METRICS_PORT=9090
MYTORRA_TRACING_ENABLED=false
MYTORRA_TRACING_JAEGER_ENDPOINT=http://localhost:14268/api/traces
