# 1. Core Service Implementation Plan

## Executive Summary

This document provides a comprehensive, step-by-step implementation plan for the MyTorra Core Service - the foundational service that manages User Accounts, Authentication, Payments, Notifications, and the Subscription Engine. This service is the single source of truth for user identity and billing across the entire platform.

**Service Name:** `core`
**Go Version:** 1.25+
**Architecture:** Clean Architecture with Hexagonal Pattern
**Status:** ✅ **FOUNDATION COMPLETE** - Basic structure implemented and building successfully

## Implementation Status

### ✅ Completed Components
- **Clean Architecture Foundation**: Domain, Application, and Adapters layers properly structured
- **Shared Package Integration**: Full integration with all shared packages (database, logger, security, cache, events, etc.)
- **Core Domain Entities**: User, Area entities with proper business logic
- **Authentication System**: Complete JWT-based auth with MFA support using real TOTP (github.com/pquerna/otp)
- **User Management**: Full CRUD operations with verification levels and area management
- **Repository Layer**: PostgreSQL repositories using shared database package
- **HTTP Layer**: REST API with proper middleware (auth, security, logging)
- **Event System**: Domain events with RabbitMQ publishing
- **Caching**: Redis-based caching for performance
- **Security**: Proper input validation, password hashing, and security headers
- **Logging**: Structured logging with shared logger interface and slog adapter pattern
- **Configuration**: Environment-based configuration management
- **Build System**: Go workspace configuration with replace directives

### 🔄 Current Implementation Phase: Foundation Complete
- All critical foundation components are implemented and building successfully
- Ready to proceed with advanced features and background workers

---

## Table of Contents

1. [Service Overview](#1-service-overview)
2. [Directory Structure](#2-directory-structure)
3. [Implementation Phases](#3-implementation-phases)
4. [Phase 1: Foundation Setup](#4-phase-1-foundation-setup)
5. [Phase 2: Domain Layer](#5-phase-2-domain-layer)
6. [Phase 3: Application Layer](#6-phase-3-application-layer)
7. [Phase 4: Infrastructure Layer](#7-phase-4-infrastructure-layer)
8. [Phase 5: API Layer](#8-phase-5-api-layer)
9. [Phase 6: Background Workers](#9-phase-6-background-workers)
10. [Phase 7: Integration & Testing](#10-phase-7-integration--testing)
11. [Database Schema](#11-database-schema)
12. [API Endpoints](#12-api-endpoints)
13. [Event Definitions](#13-event-definitions)
14. [Security Implementation](#14-security-implementation)
15. [Deployment Configuration](#15-deployment-configuration)

---

## 1. Service Overview

### 1.1 Package Dependencies (CRITICAL) - ✅ IMPLEMENTED

**This service uses the following shared packages from `/packages/`:**

| Package | Purpose | Usage | Status |
|---------|---------|-------|--------|
| `packages/database` | Database connections, transactions | All repository implementations | ✅ Integrated |
| `packages/logger` | Structured logging with interface | Throughout the service | ✅ Integrated |
| `packages/errors` | Error types and handling | All error scenarios | ✅ Integrated |
| `packages/i18n` | Internationalization | User-facing messages | ✅ Integrated |
| `packages/security` | Password hashing, TOTP validation | Authentication, input sanitization | ✅ Integrated |
| `packages/config` | Configuration management | Service initialization | ✅ Integrated |
| `packages/cache` | Redis caching layer | Session management, frequent queries | ✅ Integrated |
| `packages/events` | Event publishing with RabbitMQ | Domain events | ✅ Integrated |

**✅ All shared packages are properly integrated and working.** Real TOTP implementation using `github.com/pquerna/otp` has been added to the security package.

### 1.2 Core Responsibilities

The Core Service manages:

1. **Area Management** (Foundation for all services)
   - Dynamic area configuration (Country → Province → Town/City → Sub-areas)
   - Admin-controlled geographic boundaries
   - Area hierarchy and relationships
   - Service availability per area
   - Area validation for all services
   - Real-time area updates and propagation

2. **User Management**
   - User registration and onboarding
   - Profile management
   - Government ID verification
   - User preferences and settings
   - Area assignment and validation

3. **Authentication & Authorization**
   - JWT token management
   - OAuth2 integration (Google, Facebook)
   - Multi-factor authentication (MFA)
   - Password management and recovery
   - Session management

4. **Payment Processing**
   - Payment gateway abstraction (Stripe, local providers)
   - Wallet system for user balances
   - Transaction history and tracking
   - Refund processing
   - Payment method management
   - Currency conversion support
   - Commission calculation

5. **Notification System**
   - Email notifications (SendGrid/AWS SES)
   - SMS notifications
   - Push notifications (FCM)
   - Notification templates
   - User notification preferences
   - Delivery tracking and retry logic

6. **Subscription Engine**
   - Generic subscription management
   - Billing cycle management
   - Trial period handling
   - Usage tracking and limits
   - Subscription pause/resume
   - Upgrade/downgrade logic

### 1.2 Service Dependencies

**External Services:**
- PostgreSQL (primary datastore)
- Redis (caching, sessions, Asynq jobs)
- RabbitMQ (event bus)
- Vault/AWS Secrets Manager (secrets)
- SendGrid/AWS SES (email)
- Stripe API (payments)
- FCM (push notifications)

**Internal Dependencies:**
- `packages/auth` - Shared authentication utilities
- `packages/cache` - Redis abstraction
- `packages/config` - Configuration management
- `packages/dtos` - Shared data transfer objects
- `packages/events` - Event definitions
- `packages/logger` - Structured logging

---

## 2. Directory Structure

```
services/core/
├── cmd/
│   ├── api/
│   │   └── main.go                 # HTTP API server entry point
│   └── worker/
│       └── main.go                 # Background worker entry point
│
├── internal/
│   ├── domain/
│   │   ├── entities/
│   │   │   ├── area.go            # Area entity (country/province/town)
│   │   │   ├── user.go            # User aggregate root
│   │   │   ├── payment.go         # Payment entity
│   │   │   ├── subscription.go    # Subscription entity
│   │   │   ├── notification.go    # Notification entity
│   │   │   └── wallet.go          # Wallet entity
│   │   ├── valueobjects/
│   │   │   ├── area_type.go       # Area type value object
│   │   │   ├── area_status.go     # Area status value object
│   │   │   ├── coordinates.go     # Geographic coordinates
│   │   │   ├── email.go           # Email value object
│   │   │   ├── phone.go           # Phone value object
│   │   │   ├── money.go           # Money value object
│   │   │   ├── subscription_tier.go
│   │   │   └── notification_type.go
│   │   ├── events/
│   │   │   ├── user_events.go     # Domain events
│   │   │   ├── payment_events.go
│   │   │   └── subscription_events.go
│   │   └── errors/
│   │       └── domain_errors.go   # Domain-specific errors
│   │
│   ├── application/
│   │   ├── services/
│   │   │   ├── area_service.go
│   │   │   ├── user_service.go
│   │   │   ├── auth_service.go
│   │   │   ├── payment_service.go
│   │   │   ├── notification_service.go
│   │   │   └── subscription_service.go
│   │   ├── ports/
│   │   │   ├── repositories.go    # Repository interfaces
│   │   │   ├── payment_gateway.go # Payment provider interface
│   │   │   ├── notification_sender.go
│   │   │   ├── event_publisher.go
│   │   │   └── cache.go
│   │   ├── dtos/
│   │   │   ├── user_dto.go
│   │   │   ├── auth_dto.go
│   │   │   ├── payment_dto.go
│   │   │   └── subscription_dto.go
│   │   └── commands/
│   │       ├── create_user.go
│   │       ├── process_payment.go
│   │       └── send_notification.go
│   │
│   └── adapters/
│       ├── http/
│       │   ├── router.go          # Chi router setup
│       │   ├── middleware/
│       │   │   ├── auth.go
│       │   │   ├── rate_limit.go
│       │   │   ├── security.go
│       │   │   └── logging.go
│       │   ├── handlers/
│       │   │   ├── user_handler.go
│       │   │   ├── auth_handler.go
│       │   │   ├── payment_handler.go
│       │   │   ├── notification_handler.go
│       │   │   ├── subscription_handler.go
│       │   │   └── health_handler.go
│       │   └── validators/
│       │       └── request_validator.go
│       │
│       ├── postgres/
│       │   ├── connection.go      # DB connection management
│       │   ├── repositories/
│       │   │   ├── area_repository.go
│       │   │   ├── user_repository.go
│       │   │   ├── payment_repository.go
│       │   │   ├── subscription_repository.go
│       │   │   └── notification_repository.go
│       │   └── migrations/
│       │       └── (migration files)
│       │
│       ├── redis/
│       │   ├── cache_adapter.go
│       │   ├── session_store.go
│       │   └── rate_limiter.go
│       │
│       ├── rabbitmq/
│       │   ├── publisher.go
│       │   └── consumer.go
│       │
│       ├── payment_providers/
│       │   ├── stripe_adapter.go
│       │   └── local_provider_adapter.go
│       │
│       ├── notification_providers/
│       │   ├── sendgrid_adapter.go
│       │   ├── sms_adapter.go
│       │   └── fcm_adapter.go
│       │
│       └── worker/
│           ├── handlers/
│           │   ├── email_handler.go
│           │   ├── sms_handler.go
│           │   ├── payment_handler.go
│           │   └── subscription_handler.go
│           └── scheduler.go
│
├── configs/
│   ├── config.yaml                # Default configuration
│   ├── config.development.yaml    # Development overrides
│   └── config.production.yaml     # Production overrides
│
├── migrations/
│   ├── 001_create_areas.up.sql
│   ├── 001_create_areas.down.sql
│   ├── 002_create_users.up.sql
│   ├── 002_create_users.down.sql
│   ├── 003_create_payments.up.sql
│   ├── 003_create_payments.down.sql
│   ├── 004_create_subscriptions.up.sql
│   ├── 004_create_subscriptions.down.sql
│   ├── 005_create_notifications.up.sql
│   ├── 005_create_notifications.down.sql
│   └── 006_create_wallets.up.sql
│
├── Dockerfile.api
├── Dockerfile.worker
├── go.mod
├── go.sum
└── README.md
```

---

## 3. Implementation Phases

### Timeline Overview

| Phase | Description | Duration | Dependencies |
|-------|------------|----------|--------------|
| Phase 1 | Foundation Setup | 2 days | Infrastructure (Docker, DB) |
| Phase 2 | Domain Layer | 3 days | None |
| Phase 3 | Application Layer | 3 days | Phase 2 |
| Phase 4 | Infrastructure Layer | 5 days | Phase 3 |
| Phase 5 | API Layer | 4 days | Phase 4 |
| Phase 6 | Background Workers | 3 days | Phase 4 |
| Phase 7 | Integration & Testing | 5 days | All phases |

---

## 4. Phase 1: Foundation Setup

### 4.1 Initialize Service Module

```bash
cd services/core
go mod init github.com/paradoxe35/torra/services/core
```

### 4.2 Install Core Dependencies

**⚠️ CRITICAL: Use shared packages from `/packages/` - DO NOT install direct dependencies for functionality that exists in shared packages!**

```bash
# Web framework (OK to install - not in shared packages)
go get github.com/go-chi/chi/v5
go get github.com/go-chi/chi/v5/middleware

# ❌ DO NOT INSTALL - Use shared packages instead:
# ✅ Use github.com/paradoxe35/torra/packages/database (includes sqlx, pq)
# ✅ Use github.com/paradoxe35/torra/packages/cache (includes Redis client)
# ✅ Use github.com/paradoxe35/torra/packages/events (includes RabbitMQ)
# ✅ Use github.com/paradoxe35/torra/packages/config (includes viper)
# ✅ Use github.com/paradoxe35/torra/packages/security (includes bcrypt)
# ✅ Use github.com/paradoxe35/torra/packages/auth (includes JWT)
# ✅ Use github.com/paradoxe35/torra/packages/http (HTTP helpers)
# ✅ Use github.com/paradoxe35/torra/packages/logger (structured logging)
# ✅ Use github.com/paradoxe35/torra/packages/errors (error handling)
# ✅ Use github.com/paradoxe35/torra/packages/i18n (internationalization)

# Only install if not available in shared packages:
go get github.com/golang-migrate/migrate/v4  # For migrations
go get github.com/hibiken/asynq              # For background jobs
go get github.com/go-playground/validator/v10 # For validation
go get github.com/google/uuid                 # For UUIDs

# Testing
go get github.com/stretchr/testify
go get github.com/vektra/mockery/v2
```

**MANDATORY go.mod configuration:**

```go
// In services/core/go.mod
module github.com/paradoxe35/torra/services/core

go 1.25

// Add these replace directives to use local packages
replace (
    github.com/paradoxe35/torra/packages/auth => ../../packages/auth
    github.com/paradoxe35/torra/packages/cache => ../../packages/cache
    github.com/paradoxe35/torra/packages/config => ../../packages/config
    github.com/paradoxe35/torra/packages/database => ../../packages/database
    github.com/paradoxe35/torra/packages/errors => ../../packages/errors
    github.com/paradoxe35/torra/packages/events => ../../packages/events
    github.com/paradoxe35/torra/packages/http => ../../packages/http
    github.com/paradoxe35/torra/packages/i18n => ../../packages/i18n
    github.com/paradoxe35/torra/packages/logger => ../../packages/logger
    github.com/paradoxe35/torra/packages/security => ../../packages/security
)
```

### 4.3 Configuration Setup

Create `configs/config.yaml`:

```yaml
server:
  name: core-service
  port: 8080
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 120s
  shutdown_timeout: 30s

database:
  host: localhost
  port: 5432
  name: mytorra_core
  user: mytorra
  password: ${DB_PASSWORD}
  max_connections: 25
  max_idle_connections: 5
  connection_lifetime: 5m
  ssl_mode: disable

redis:
  host: localhost
  port: 6379
  password: ${REDIS_PASSWORD}
  db: 0
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3
  read_timeout: 3s
  write_timeout: 3s

rabbitmq:
  url: amqp://guest:guest@localhost:5672/
  exchange: mytorra.events
  exchange_type: topic
  queue_prefix: core-service

jwt:
  secret: ${JWT_SECRET}
  access_token_duration: 15m
  refresh_token_duration: 7d
  issuer: mytorra-core

stripe:
  secret_key: ${STRIPE_SECRET_KEY}
  webhook_secret: ${STRIPE_WEBHOOK_SECRET}

sendgrid:
  api_key: ${SENDGRID_API_KEY}
  from_email: <EMAIL>
  from_name: MyTorra

fcm:
  project_id: ${FCM_PROJECT_ID}
  credentials_file: ${FCM_CREDENTIALS_FILE}

log:
  level: info
  format: json

metrics:
  enabled: true
  path: /metrics
  port: 9090

rate_limit:
  requests_per_second: 10
  burst: 20
```

---

## 5. Phase 2: Domain Layer

### 5.1 Area Entity (Foundation for all services)

```go
// internal/domain/entities/area.go
package entities

import (
    "time"
    "github.com/google/uuid"
    "github.com/paradoxe35/torra/services/core/internal/domain/valueobjects"
)

type Area struct {
    ID              uuid.UUID
    ParentID        *uuid.UUID // Null for countries

    // Hierarchy
    Type            valueobjects.AreaType // country, province, town, subarea
    Name            string
    Code            string // Unique code for the area

    // Geographic data
    BoundaryPolygon []valueobjects.Coordinates // GeoJSON polygon
    CenterLat       float64
    CenterLng       float64
    OperationalRadius int // in kilometers

    // Configuration
    Status          valueobjects.AreaStatus // active, inactive, testing
    ServiceAvailability map[string]bool // Which services are available
    Settings        map[string]interface{}

    // Area Behavior Settings
    SwitchingMode   AreaSwitchingMode // automatic, manual, hybrid, locked
    AllowCrossTrade bool              // Allow cross-area trading
    NearbyAreaIDs   []uuid.UUID       // Related/nearby areas
    TransitionZones []TransitionZone  // Overlapping boundaries

    // Operational data
    Timezone        string
    Languages       []string
    Currency        string
    TaxRate         float64

    // Service limits
    MinOrderValue   float64
    DeliveryFeeStructure map[string]interface{}
    CrossAreaFees   map[string]float64 // Fees for cross-area operations
    OperationalHours map[string]interface{}

    // Metadata
    CreatedAt       time.Time
    UpdatedAt       time.Time
    ActivatedAt     *time.Time
    DeactivatedAt   *time.Time

    // Relationships (loaded separately)
    Children        []Area
    Parent          *Area
}

type AreaSwitchingMode string

const (
    SwitchingModeAutomatic AreaSwitchingMode = "automatic"
    SwitchingModeManual    AreaSwitchingMode = "manual"
    SwitchingModeHybrid    AreaSwitchingMode = "hybrid"
    SwitchingModeLocked    AreaSwitchingMode = "locked"
)

// Domain methods
func (a *Area) IsActive() bool {
    return a.Status == valueobjects.AreaStatusActive
}

func (a *Area) IsServiceAvailable(service string) bool {
    if !a.IsActive() {
        return false
    }
    available, exists := a.ServiceAvailability[service]
    return exists && available
}

func (a *Area) GetFullPath() []string {
    path := []string{a.Name}
    if a.Parent != nil {
        path = append(a.Parent.GetFullPath(), path...)
    }
    return path
}

func (a *Area) ContainsCoordinate(lat, lng float64) bool {
    // Check if coordinate is within boundary polygon
    // Implementation would use point-in-polygon algorithm
    return true // Placeholder
}

func (a *Area) CanAddSubArea() bool {
    // Business rules for adding sub-areas
    switch a.Type {
    case valueobjects.AreaTypeCountry:
        return true // Can add provinces
    case valueobjects.AreaTypeProvince:
        return true // Can add towns
    case valueobjects.AreaTypeTown:
        return true // Can add subareas
    case valueobjects.AreaTypeSubarea:
        return false // Cannot add more levels
    default:
        return false
    }
}
```

### 5.2 User Entity

```go
// internal/domain/entities/user.go
package entities

import (
    "time"
    "github.com/google/uuid"
    "github.com/paradoxe35/torra/services/core/internal/domain/valueobjects"
)

type User struct {
    ID              uuid.UUID
    Email           valueobjects.Email
    Phone           valueobjects.Phone
    Name            string
    PasswordHash    string
    ProfileImageURL string

    // Verification
    EmailVerified   bool
    PhoneVerified   bool
    IDVerified      bool
    VerificationLevel int // 1-5

    // OAuth
    GoogleID        *string
    FacebookID      *string

    // MFA
    MFAEnabled      bool
    MFASecret       *string

    // Location
    AreaID          uuid.UUID // Reference to areas table
    LastKnownLat    *float64
    LastKnownLng    *float64

    // Metadata
    Status          UserStatus
    LastLoginAt     *time.Time
    CreatedAt       time.Time
    UpdatedAt       time.Time
    DeletedAt       *time.Time
}

type UserStatus string

const (
    UserStatusActive    UserStatus = "active"
    UserStatusSuspended UserStatus = "suspended"
    UserStatusBanned    UserStatus = "banned"
    UserStatusDeleted   UserStatus = "deleted"
)

// Domain methods
func (u *User) CanLogin() bool {
    return u.Status == UserStatusActive && u.EmailVerified
}

func (u *User) RequiresMFA() bool {
    return u.MFAEnabled && u.MFASecret != nil
}

func (u *User) UpdateVerificationLevel() {
    level := 0
    if u.EmailVerified {
        level++
    }
    if u.PhoneVerified {
        level++
    }
    if u.IDVerified {
        level++
    }
    // Additional business logic for levels 4 and 5
    u.VerificationLevel = level
}
```

### 5.2 Payment Entity

```go
// internal/domain/entities/payment.go
package entities

import (
    "time"
    "github.com/google/uuid"
    "github.com/paradoxe35/torra/services/core/internal/domain/valueobjects"
)

type Payment struct {
    ID              uuid.UUID
    UserID          uuid.UUID
    Amount          valueobjects.Money
    Type            PaymentType
    Method          PaymentMethod
    Status          PaymentStatus

    // Provider details
    ProviderID      string // Stripe payment intent ID, etc.
    ProviderName    string

    // Transaction details
    Description     string
    Metadata        map[string]interface{}

    // Refund information
    RefundedAmount  *valueobjects.Money
    RefundReason    *string

    // Commission
    Commission      *valueobjects.Money
    CommissionRate  float64

    // Timestamps
    ProcessedAt     *time.Time
    FailedAt        *time.Time
    RefundedAt      *time.Time
    CreatedAt       time.Time
    UpdatedAt       time.Time
}

type PaymentType string
type PaymentMethod string
type PaymentStatus string

const (
    PaymentTypePurchase     PaymentType = "purchase"
    PaymentTypeSubscription PaymentType = "subscription"
    PaymentTypeWalletTopUp  PaymentType = "wallet_topup"
    PaymentTypeCommission   PaymentType = "commission"

    PaymentMethodCard       PaymentMethod = "card"
    PaymentMethodWallet     PaymentMethod = "wallet"
    PaymentMethodMobileMoney PaymentMethod = "mobile_money"
    PaymentMethodBankTransfer PaymentMethod = "bank_transfer"

    PaymentStatusPending    PaymentStatus = "pending"
    PaymentStatusProcessing PaymentStatus = "processing"
    PaymentStatusCompleted  PaymentStatus = "completed"
    PaymentStatusFailed     PaymentStatus = "failed"
    PaymentStatusRefunded   PaymentStatus = "refunded"
)

// Domain methods
func (p *Payment) CanBeRefunded() bool {
    return p.Status == PaymentStatusCompleted && p.RefundedAmount == nil
}

func (p *Payment) CalculateCommission(rate float64) valueobjects.Money {
    return p.Amount.Multiply(rate)
}
```

### 5.3 Subscription Entity

```go
// internal/domain/entities/subscription.go
package entities

import (
    "time"
    "github.com/google/uuid"
    "github.com/paradoxe35/torra/services/core/internal/domain/valueobjects"
)

type Subscription struct {
    ID              uuid.UUID
    UserID          uuid.UUID
    EntityID        uuid.UUID // ID of the entity being subscribed (seller, restaurant, etc.)
    EntityType      string    // "seller", "restaurant", etc.

    // Plan details
    Tier            valueobjects.SubscriptionTier
    PlanName        string
    Price           valueobjects.Money
    BillingCycle    BillingCycle

    // Status
    Status          SubscriptionStatus

    // Trial
    TrialEndsAt     *time.Time
    IsTrialing      bool

    // Billing
    CurrentPeriodStart time.Time
    CurrentPeriodEnd   time.Time
    NextBillingDate    *time.Time

    // Usage limits
    UsageLimits     map[string]int
    CurrentUsage    map[string]int

    // Pause/Resume
    PausedAt        *time.Time
    ResumeAt        *time.Time

    // Cancellation
    CanceledAt      *time.Time
    CancelReason    *string

    // Timestamps
    ActivatedAt     *time.Time
    CreatedAt       time.Time
    UpdatedAt       time.Time
}

type BillingCycle string
type SubscriptionStatus string

const (
    BillingCycleMonthly BillingCycle = "monthly"
    BillingCycleAnnual  BillingCycle = "annual"

    SubscriptionStatusActive    SubscriptionStatus = "active"
    SubscriptionStatusTrialing  SubscriptionStatus = "trialing"
    SubscriptionStatusPaused    SubscriptionStatus = "paused"
    SubscriptionStatusCanceled  SubscriptionStatus = "canceled"
    SubscriptionStatusExpired   SubscriptionStatus = "expired"
)

// Domain methods
func (s *Subscription) CanUpgrade() bool {
    return s.Status == SubscriptionStatusActive && !s.IsTrialing
}

func (s *Subscription) IsWithinUsageLimits(feature string) bool {
    limit, hasLimit := s.UsageLimits[feature]
    if !hasLimit {
        return true // No limit set
    }

    usage := s.CurrentUsage[feature]
    return usage < limit
}

func (s *Subscription) DaysUntilRenewal() int {
    if s.NextBillingDate == nil {
        return -1
    }
    return int(time.Until(*s.NextBillingDate).Hours() / 24)
}
```

---

## 6. Phase 3: Application Layer

### 6.1 User Service

```go
// internal/application/services/user_service.go
package services

import (
    "context"
    "fmt"

    "github.com/google/uuid"
    "github.com/paradoxe35/torra/services/core/internal/application/ports"
    "github.com/paradoxe35/torra/services/core/internal/domain/entities"
    "github.com/paradoxe35/torra/services/core/internal/domain/events"
)

type UserService struct {
    userRepo        ports.UserRepository
    eventPublisher  ports.EventPublisher
    cache          ports.Cache
    logger         ports.Logger
}

func NewUserService(
    userRepo ports.UserRepository,
    eventPublisher ports.EventPublisher,
    cache ports.Cache,
    logger ports.Logger,
) *UserService {
    return &UserService{
        userRepo:       userRepo,
        eventPublisher: eventPublisher,
        cache:         cache,
        logger:        logger,
    }
}

func (s *UserService) CreateUser(ctx context.Context, req CreateUserRequest) (*entities.User, error) {
    // Validate request
    if err := req.Validate(); err != nil {
        return nil, fmt.Errorf("validation failed: %w", err)
    }

    // Check if user exists
    existing, _ := s.userRepo.FindByEmail(ctx, req.Email)
    if existing != nil {
        return nil, ErrUserAlreadyExists
    }

    // Validate area exists and is active
    area, err := s.areaService.GetAreaByID(ctx, req.AreaID)
    if err != nil {
        return nil, fmt.Errorf("invalid area: %w", err)
    }
    if !area.IsActive() {
        return nil, ErrAreaNotActive
    }

    // Create user entity
    user := &entities.User{
        ID:       uuid.New(),
        Email:    req.Email,
        Phone:    req.Phone,
        Name:     req.Name,
        AreaID:   req.AreaID,
        Status:   entities.UserStatusActive,
        CreatedAt: time.Now(),
        UpdatedAt: time.Now(),
    }

    // Hash password
    hashedPassword, err := hashPassword(req.Password)
    if err != nil {
        return nil, fmt.Errorf("failed to hash password: %w", err)
    }
    user.PasswordHash = hashedPassword

    // Save to repository
    if err := s.userRepo.Save(ctx, user); err != nil {
        return nil, fmt.Errorf("failed to save user: %w", err)
    }

    // Publish event
    event := events.UserCreatedEvent{
        UserID:    user.ID,
        Email:     user.Email.String(),
        AreaID:    user.AreaID,
        CreatedAt: user.CreatedAt,
    }

    if err := s.eventPublisher.Publish(ctx, event); err != nil {
        s.logger.Error("failed to publish user created event", "error", err)
    }

    // Invalidate cache
    s.cache.Delete(ctx, fmt.Sprintf("user:%s", user.ID))

    return user, nil
}

func (s *UserService) GetUser(ctx context.Context, userID uuid.UUID) (*entities.User, error) {
    // Try cache first
    cacheKey := fmt.Sprintf("user:%s", userID)
    if cached, err := s.cache.Get(ctx, cacheKey); err == nil {
        var user entities.User
        if err := json.Unmarshal(cached, &user); err == nil {
            return &user, nil
        }
    }

    // Get from repository
    user, err := s.userRepo.FindByID(ctx, userID)
    if err != nil {
        return nil, fmt.Errorf("failed to find user: %w", err)
    }

    // Update cache
    if data, err := json.Marshal(user); err == nil {
        s.cache.Set(ctx, cacheKey, data, 5*time.Minute)
    }

    return user, nil
}

func (s *UserService) UpdateVerificationStatus(ctx context.Context, userID uuid.UUID, verificationType string) error {
    user, err := s.userRepo.FindByID(ctx, userID)
    if err != nil {
        return fmt.Errorf("user not found: %w", err)
    }

    switch verificationType {
    case "email":
        user.EmailVerified = true
    case "phone":
        user.PhoneVerified = true
    case "id":
        user.IDVerified = true
    default:
        return fmt.Errorf("invalid verification type: %s", verificationType)
    }

    user.UpdateVerificationLevel()
    user.UpdatedAt = time.Now()

    if err := s.userRepo.Update(ctx, user); err != nil {
        return fmt.Errorf("failed to update user: %w", err)
    }

    // Invalidate cache
    s.cache.Delete(ctx, fmt.Sprintf("user:%s", userID))

    return nil
}
```

### 6.2 Authentication Service

```go
// internal/application/services/auth_service.go
package services

import (
    "context"
    "time"

    "github.com/golang-jwt/jwt/v5"
    "golang.org/x/crypto/bcrypt"
)

type AuthService struct {
    userRepo       ports.UserRepository
    tokenSecret    []byte
    tokenIssuer    string
    accessDuration time.Duration
    refreshDuration time.Duration
    cache          ports.Cache
    logger         ports.Logger
}

func (s *AuthService) Login(ctx context.Context, req LoginRequest) (*AuthResponse, error) {
    // Find user by email
    user, err := s.userRepo.FindByEmail(ctx, req.Email)
    if err != nil {
        return nil, ErrInvalidCredentials
    }

    // Check if user can login
    if !user.CanLogin() {
        return nil, ErrAccountSuspended
    }

    // Verify password
    if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
        return nil, ErrInvalidCredentials
    }

    // Check MFA if enabled
    if user.RequiresMFA() && req.MFACode == "" {
        return &AuthResponse{
            RequiresMFA: true,
        }, nil
    }

    if user.RequiresMFA() {
        if !s.verifyMFACode(user.MFASecret, req.MFACode) {
            return nil, ErrInvalidMFACode
        }
    }

    // Generate tokens
    accessToken, err := s.generateAccessToken(user)
    if err != nil {
        return nil, fmt.Errorf("failed to generate access token: %w", err)
    }

    refreshToken, err := s.generateRefreshToken(user)
    if err != nil {
        return nil, fmt.Errorf("failed to generate refresh token: %w", err)
    }

    // Update last login
    user.LastLoginAt = &time.Now()
    s.userRepo.Update(ctx, user)

    // Store refresh token in Redis
    s.cache.Set(ctx, fmt.Sprintf("refresh:%s", user.ID), refreshToken, s.refreshDuration)

    return &AuthResponse{
        AccessToken:  accessToken,
        RefreshToken: refreshToken,
        ExpiresIn:    int(s.accessDuration.Seconds()),
        User:         user,
    }, nil
}

func (s *AuthService) generateAccessToken(user *entities.User) (string, error) {
    claims := jwt.MapClaims{
        "sub":    user.ID.String(),
        "email":  user.Email.String(),
        "name":   user.Name,
        "area_id": user.AreaID.String(),
        "iat":    time.Now().Unix(),
        "exp":    time.Now().Add(s.accessDuration).Unix(),
        "iss":    s.tokenIssuer,
    }

    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString(s.tokenSecret)
}
```

---

## 7. Phase 4: Infrastructure Layer

### 7.1 PostgreSQL Repository Implementation

```go
// internal/adapters/postgres/repositories/user_repository.go
package repositories

import (
    "context"
    "database/sql"
    "fmt"

    "github.com/google/uuid"
    "github.com/jmoiron/sqlx"
    "github.com/paradoxe35/torra/services/core/internal/domain/entities"
)

type PostgresUserRepository struct {
    db *sqlx.DB
}

func NewPostgresUserRepository(db *sqlx.DB) *PostgresUserRepository {
    return &PostgresUserRepository{db: db}
}

const getUserByIDQuery = `
    SELECT
        id, email, phone, name, password_hash,
        profile_image_url, email_verified, phone_verified,
        id_verified, verification_level, google_id, facebook_id,
        mfa_enabled, mfa_secret, area, last_known_lat, last_known_lng,
        status, last_login_at, created_at, updated_at, deleted_at
    FROM users
    WHERE id = $1 AND deleted_at IS NULL
`

func (r *PostgresUserRepository) FindByID(ctx context.Context, id uuid.UUID) (*entities.User, error) {
    var user entities.User
    err := r.db.GetContext(ctx, &user, getUserByIDQuery, id)
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, ErrUserNotFound
        }
        return nil, fmt.Errorf("failed to get user by id: %w", err)
    }
    return &user, nil
}

const saveUserQuery = `
    INSERT INTO users (
        id, email, phone, name, password_hash,
        profile_image_url, email_verified, phone_verified,
        id_verified, verification_level, area, status,
        created_at, updated_at
    ) VALUES (
        :id, :email, :phone, :name, :password_hash,
        :profile_image_url, :email_verified, :phone_verified,
        :id_verified, :verification_level, :area, :status,
        :created_at, :updated_at
    )
`

func (r *PostgresUserRepository) Save(ctx context.Context, user *entities.User) error {
    _, err := r.db.NamedExecContext(ctx, saveUserQuery, user)
    if err != nil {
        return fmt.Errorf("failed to save user: %w", err)
    }
    return nil
}

func (r *PostgresUserRepository) Update(ctx context.Context, user *entities.User) error {
    query := `
        UPDATE users SET
            email = :email,
            phone = :phone,
            name = :name,
            profile_image_url = :profile_image_url,
            email_verified = :email_verified,
            phone_verified = :phone_verified,
            id_verified = :id_verified,
            verification_level = :verification_level,
            mfa_enabled = :mfa_enabled,
            mfa_secret = :mfa_secret,
            area = :area,
            last_known_lat = :last_known_lat,
            last_known_lng = :last_known_lng,
            status = :status,
            last_login_at = :last_login_at,
            updated_at = :updated_at
        WHERE id = :id AND deleted_at IS NULL
    `

    _, err := r.db.NamedExecContext(ctx, query, user)
    if err != nil {
        return fmt.Errorf("failed to update user: %w", err)
    }
    return nil
}
```

### 7.2 Redis Cache Implementation

```go
// internal/adapters/redis/cache_adapter.go
package redis

import (
    "context"
    "time"

    "github.com/redis/go-redis/v9"
)

type RedisCacheAdapter struct {
    client *redis.Client
}

func NewRedisCacheAdapter(client *redis.Client) *RedisCacheAdapter {
    return &RedisCacheAdapter{client: client}
}

func (c *RedisCacheAdapter) Get(ctx context.Context, key string) ([]byte, error) {
    val, err := c.client.Get(ctx, key).Result()
    if err != nil {
        return nil, err
    }
    return []byte(val), nil
}

func (c *RedisCacheAdapter) Set(ctx context.Context, key string, value []byte, ttl time.Duration) error {
    return c.client.Set(ctx, key, value, ttl).Err()
}

func (c *RedisCacheAdapter) Delete(ctx context.Context, key string) error {
    return c.client.Del(ctx, key).Err()
}
```

---

## 8. Phase 5: API Layer

### 8.1 Router Setup

```go
// internal/adapters/http/router.go
package http

import (
    "github.com/go-chi/chi/v5"
    "github.com/go-chi/chi/v5/middleware"
)

func NewRouter(handlers *Handlers, middlewares *Middlewares) *chi.Mux {
    r := chi.NewRouter()

    // Global middleware
    r.Use(middleware.Recovery)
    r.Use(middleware.RequestID)
    r.Use(middleware.RealIP)
    r.Use(middlewares.Logger)
    r.Use(middleware.Timeout(30 * time.Second))
    r.Use(middlewares.Security)
    r.Use(middlewares.RateLimit)

    // Health checks
    r.Get("/health", handlers.Health.Check)
    r.Get("/ready", handlers.Health.Ready)

    // API routes
    r.Route("/api/v1", func(r chi.Router) {
        // Authentication routes
        r.Route("/auth", func(r chi.Router) {
            r.Post("/register", handlers.Auth.Register)
            r.Post("/login", handlers.Auth.Login)
            r.Post("/refresh", handlers.Auth.RefreshToken)
            r.Post("/logout", handlers.Auth.Logout)
            r.Post("/forgot-password", handlers.Auth.ForgotPassword)
            r.Post("/reset-password", handlers.Auth.ResetPassword)
            r.Post("/verify-email", handlers.Auth.VerifyEmail)
            r.Post("/verify-phone", handlers.Auth.VerifyPhone)

            // OAuth routes
            r.Get("/oauth/google", handlers.Auth.GoogleOAuth)
            r.Get("/oauth/google/callback", handlers.Auth.GoogleCallback)
            r.Get("/oauth/facebook", handlers.Auth.FacebookOAuth)
            r.Get("/oauth/facebook/callback", handlers.Auth.FacebookCallback)
        })

        // Protected routes
        r.Group(func(r chi.Router) {
            r.Use(middlewares.Authenticate)

            // User routes
            r.Route("/users", func(r chi.Router) {
                r.Get("/me", handlers.User.GetCurrentUser)
                r.Put("/me", handlers.User.UpdateCurrentUser)
                r.Delete("/me", handlers.User.DeleteCurrentUser)
                r.Post("/me/mfa/enable", handlers.User.EnableMFA)
                r.Post("/me/mfa/disable", handlers.User.DisableMFA)
                r.Put("/me/area", handlers.User.UpdateArea)
                r.Get("/me/verification-status", handlers.User.GetVerificationStatus)
                r.Post("/me/verify-id", handlers.User.VerifyGovernmentID)
            })

            // Payment routes
            r.Route("/payments", func(r chi.Router) {
                r.Get("/", handlers.Payment.ListPayments)
                r.Get("/{id}", handlers.Payment.GetPayment)
                r.Post("/", handlers.Payment.CreatePayment)
                r.Post("/{id}/refund", handlers.Payment.RefundPayment)
                r.Get("/methods", handlers.Payment.ListPaymentMethods)
                r.Post("/methods", handlers.Payment.AddPaymentMethod)
                r.Delete("/methods/{id}", handlers.Payment.RemovePaymentMethod)
            })

            // Wallet routes
            r.Route("/wallet", func(r chi.Router) {
                r.Get("/", handlers.Wallet.GetBalance)
                r.Get("/transactions", handlers.Wallet.GetTransactions)
                r.Post("/topup", handlers.Wallet.TopUp)
                r.Post("/withdraw", handlers.Wallet.Withdraw)
            })

            // Subscription routes
            r.Route("/subscriptions", func(r chi.Router) {
                r.Get("/", handlers.Subscription.ListSubscriptions)
                r.Get("/{id}", handlers.Subscription.GetSubscription)
                r.Post("/", handlers.Subscription.CreateSubscription)
                r.Put("/{id}/upgrade", handlers.Subscription.UpgradeSubscription)
                r.Put("/{id}/downgrade", handlers.Subscription.DowngradeSubscription)
                r.Put("/{id}/pause", handlers.Subscription.PauseSubscription)
                r.Put("/{id}/resume", handlers.Subscription.ResumeSubscription)
                r.Delete("/{id}", handlers.Subscription.CancelSubscription)
                r.Get("/{id}/usage", handlers.Subscription.GetUsage)
            })

            // Notification preferences
            r.Route("/notifications", func(r chi.Router) {
                r.Get("/preferences", handlers.Notification.GetPreferences)
                r.Put("/preferences", handlers.Notification.UpdatePreferences)
                r.Get("/", handlers.Notification.ListNotifications)
                r.Put("/{id}/read", handlers.Notification.MarkAsRead)
                r.Put("/read-all", handlers.Notification.MarkAllAsRead)
            })
        })
    })

    // Metrics endpoint
    r.Handle("/metrics", promhttp.Handler())

    return r
}
```

---

## 9. Phase 6: Background Workers

### 9.1 Worker Setup

```go
// cmd/worker/main.go
package main

import (
    "context"
    "log"
    "os"
    "os/signal"
    "syscall"

    "github.com/hibiken/asynq"
)

func main() {
    // Load configuration
    cfg, err := config.Load()
    if err != nil {
        log.Fatal("failed to load config:", err)
    }

    // Setup dependencies
    deps, err := setupDependencies(cfg)
    if err != nil {
        log.Fatal("failed to setup dependencies:", err)
    }

    // Create Asynq server
    srv := asynq.NewServer(
        asynq.RedisClientOpt{Addr: cfg.Redis.Addr},
        asynq.Config{
            Concurrency: 10,
            Queues: map[string]int{
                "critical": 6,
                "default":  3,
                "low":      1,
            },
            ErrorHandler: asynq.ErrorHandlerFunc(handleError),
            RetryDelayFunc: exponentialBackoff,
        },
    )

    // Create mux and register handlers
    mux := asynq.NewServeMux()

    // Email handlers
    mux.HandleFunc("email:welcome", deps.EmailHandler.HandleWelcomeEmail)
    mux.HandleFunc("email:verification", deps.EmailHandler.HandleVerificationEmail)
    mux.HandleFunc("email:password-reset", deps.EmailHandler.HandlePasswordResetEmail)

    // SMS handlers
    mux.HandleFunc("sms:verification", deps.SMSHandler.HandleVerificationSMS)
    mux.HandleFunc("sms:notification", deps.SMSHandler.HandleNotificationSMS)

    // Payment handlers
    mux.HandleFunc("payment:process", deps.PaymentHandler.ProcessPayment)
    mux.HandleFunc("payment:refund", deps.PaymentHandler.ProcessRefund)

    // Subscription handlers
    mux.HandleFunc("subscription:renew", deps.SubscriptionHandler.RenewSubscription)
    mux.HandleFunc("subscription:expire", deps.SubscriptionHandler.ExpireSubscription)
    mux.HandleFunc("subscription:reminder", deps.SubscriptionHandler.SendRenewalReminder)

    // Start worker
    if err := srv.Run(mux); err != nil {
        log.Fatal("failed to run worker:", err)
    }
}
```

---

## 10. Phase 7: Integration & Testing

### 10.1 Unit Test Example

```go
// internal/application/services/user_service_test.go
package services

import (
    "context"
    "testing"

    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/mock"
)

func TestUserService_CreateUser(t *testing.T) {
    // Arrange
    mockRepo := mocks.NewMockUserRepository(t)
    mockPublisher := mocks.NewMockEventPublisher(t)
    mockCache := mocks.NewMockCache(t)
    mockLogger := mocks.NewMockLogger(t)

    service := NewUserService(mockRepo, mockPublisher, mockCache, mockLogger)

    req := CreateUserRequest{
        Email:    "<EMAIL>",
        Phone:    "+250780123456",
        Name:     "Test User",
        Password: "SecurePassword123!",
        Area:     "bukavu",
    }

    mockRepo.On("FindByEmail", mock.Anything, req.Email).Return(nil, ErrUserNotFound)
    mockRepo.On("Save", mock.Anything, mock.AnythingOfType("*entities.User")).Return(nil)
    mockPublisher.On("Publish", mock.Anything, mock.AnythingOfType("events.UserCreatedEvent")).Return(nil)
    mockCache.On("Delete", mock.Anything, mock.AnythingOfType("string")).Return(nil)

    // Act
    user, err := service.CreateUser(context.Background(), req)

    // Assert
    assert.NoError(t, err)
    assert.NotNil(t, user)
    assert.Equal(t, req.Email, user.Email.String())
    assert.Equal(t, req.Name, user.Name)
    assert.Equal(t, req.Area, user.Area)

    mockRepo.AssertExpectations(t)
    mockPublisher.AssertExpectations(t)
    mockCache.AssertExpectations(t)
}
```

### 10.2 Integration Test Example

```go
// tests/integration/user_repository_test.go
package integration

import (
    "context"
    "testing"

    "github.com/stretchr/testify/assert"
    "github.com/testcontainers/testcontainers-go"
    "github.com/testcontainers/testcontainers-go/modules/postgres"
)

func TestUserRepository_Integration(t *testing.T) {
    ctx := context.Background()

    // Start PostgreSQL container
    postgresContainer, err := postgres.RunContainer(ctx,
        testcontainers.WithImage("postgres:15"),
        postgres.WithDatabase("test"),
        postgres.WithUsername("test"),
        postgres.WithPassword("test"),
    )
    assert.NoError(t, err)
    defer postgresContainer.Terminate(ctx)

    // Get connection string
    connStr, err := postgresContainer.ConnectionString(ctx)
    assert.NoError(t, err)

    // Connect to database
    db, err := sqlx.Connect("postgres", connStr)
    assert.NoError(t, err)

    // Run migrations
    err = runMigrations(db)
    assert.NoError(t, err)

    // Create repository
    repo := repositories.NewPostgresUserRepository(db)

    // Test Save
    user := &entities.User{
        ID:    uuid.New(),
        Email: "<EMAIL>",
        Name:  "Test User",
        Area:  "bukavu",
    }

    err = repo.Save(ctx, user)
    assert.NoError(t, err)

    // Test FindByID
    found, err := repo.FindByID(ctx, user.ID)
    assert.NoError(t, err)
    assert.Equal(t, user.ID, found.ID)
    assert.Equal(t, user.Email, found.Email)
}
```

---

## 11. Database Schema

### 11.1 Areas Table (Foundation for all services)

```sql
-- migrations/001_create_areas.up.sql
CREATE TABLE IF NOT EXISTS areas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_id UUID REFERENCES areas(id),

    -- Area hierarchy
    type VARCHAR(20) NOT NULL, -- 'country', 'province', 'town', 'subarea'
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE,

    -- Geographic data
    boundary_polygon JSONB, -- GeoJSON polygon for area boundaries
    center_lat DECIMAL(10, 8),
    center_lng DECIMAL(11, 8),
    operational_radius INT, -- in kilometers

    -- Configuration
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    service_availability JSONB, -- {"mytorra": true, "torra_shop": true, ...}
    settings JSONB, -- Area-specific settings

    -- Area Behavior Settings
    switching_mode VARCHAR(20) NOT NULL DEFAULT 'manual',
    allow_cross_trade BOOLEAN DEFAULT FALSE,
    nearby_area_ids UUID[], -- Array of related area IDs
    transition_zones JSONB, -- Overlapping boundary configurations

    -- Operational data
    timezone VARCHAR(50),
    languages TEXT[], -- Array of supported languages
    currency VARCHAR(3) DEFAULT 'USD',
    tax_rate DECIMAL(5, 4),

    -- Service limits
    min_order_value DECIMAL(10, 2),
    delivery_fee_structure JSONB,
    cross_area_fees JSONB, -- {"shipping": 5.00, "express": 10.00}
    operational_hours JSONB,

    -- Metadata
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    activated_at TIMESTAMP,
    deactivated_at TIMESTAMP,

    CONSTRAINT valid_area_type CHECK (type IN ('country', 'province', 'town', 'subarea')),
    CONSTRAINT valid_status CHECK (status IN ('active', 'inactive', 'testing', 'suspended')),
    CONSTRAINT valid_switching_mode CHECK (switching_mode IN ('automatic', 'manual', 'hybrid', 'locked'))
);

-- Additional tables for area relationships
CREATE TABLE IF NOT EXISTS area_relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    area_id UUID NOT NULL REFERENCES areas(id),
    related_area_id UUID NOT NULL REFERENCES areas(id),
    relationship_type VARCHAR(50) NOT NULL, -- 'sister_city', 'metro_zone', 'trade_partner'
    bidirectional BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),

    CONSTRAINT unique_area_relationship UNIQUE (area_id, related_area_id, relationship_type)
);

CREATE INDEX idx_areas_parent_id ON areas(parent_id);
CREATE INDEX idx_areas_type ON areas(type);
CREATE INDEX idx_areas_status ON areas(status);
CREATE INDEX idx_areas_name ON areas(name);
CREATE INDEX idx_areas_code ON areas(code);

-- Create hierarchical path view for easier querying
CREATE OR REPLACE VIEW area_hierarchy AS
WITH RECURSIVE area_path AS (
    SELECT
        id,
        parent_id,
        name,
        type,
        ARRAY[name] as path,
        1 as level
    FROM areas
    WHERE parent_id IS NULL

    UNION ALL

    SELECT
        a.id,
        a.parent_id,
        a.name,
        a.type,
        ap.path || a.name,
        ap.level + 1
    FROM areas a
    INNER JOIN area_path ap ON a.parent_id = ap.id
)
SELECT * FROM area_path;
```

### 11.2 Users Table

```sql
-- migrations/002_create_users.up.sql
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    phone VARCHAR(20) UNIQUE,
    name VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    profile_image_url TEXT,

    -- Verification
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    id_verified BOOLEAN DEFAULT FALSE,
    verification_level INT DEFAULT 0,

    -- OAuth
    google_id VARCHAR(255) UNIQUE,
    facebook_id VARCHAR(255) UNIQUE,

    -- MFA
    mfa_enabled BOOLEAN DEFAULT FALSE,
    mfa_secret VARCHAR(255),

    -- Location
    area_id UUID NOT NULL REFERENCES areas(id),
    last_known_lat DECIMAL(10, 8),
    last_known_lng DECIMAL(11, 8),

    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    last_login_at TIMESTAMP,

    -- Timestamps
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP,

    CONSTRAINT valid_status CHECK (status IN ('active', 'suspended', 'banned', 'deleted'))
);

CREATE INDEX idx_users_email ON users(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_phone ON users(phone) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_area_id ON users(area_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_status ON users(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_created_at ON users(created_at);
```

### 11.3 Payments Table

```sql
-- migrations/003_create_payments.up.sql
CREATE TABLE IF NOT EXISTS payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    type VARCHAR(20) NOT NULL,
    method VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',

    -- Provider details
    provider_id VARCHAR(255),
    provider_name VARCHAR(50),

    -- Transaction details
    description TEXT,
    metadata JSONB,

    -- Refund information
    refunded_amount DECIMAL(10, 2),
    refund_reason TEXT,

    -- Commission
    commission DECIMAL(10, 2),
    commission_rate DECIMAL(5, 4),

    -- Timestamps
    processed_at TIMESTAMP,
    failed_at TIMESTAMP,
    refunded_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),

    CONSTRAINT valid_type CHECK (type IN ('purchase', 'subscription', 'wallet_topup', 'commission')),
    CONSTRAINT valid_method CHECK (method IN ('card', 'wallet', 'mobile_money', 'bank_transfer')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'refunded'))
);

CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_created_at ON payments(created_at);
CREATE INDEX idx_payments_provider_id ON payments(provider_id);
```

### 11.4 Subscriptions Table

```sql
-- migrations/004_create_subscriptions.up.sql
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    entity_id UUID NOT NULL,
    entity_type VARCHAR(50) NOT NULL,

    -- Plan details
    tier VARCHAR(20) NOT NULL,
    plan_name VARCHAR(100) NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    billing_cycle VARCHAR(20) NOT NULL,

    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'active',

    -- Trial
    trial_ends_at TIMESTAMP,
    is_trialing BOOLEAN DEFAULT FALSE,

    -- Billing
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    next_billing_date TIMESTAMP,

    -- Usage limits
    usage_limits JSONB,
    current_usage JSONB,

    -- Pause/Resume
    paused_at TIMESTAMP,
    resume_at TIMESTAMP,

    -- Cancellation
    canceled_at TIMESTAMP,
    cancel_reason TEXT,

    -- Timestamps
    activated_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),

    CONSTRAINT valid_tier CHECK (tier IN ('basic', 'professional', 'premium', 'enterprise')),
    CONSTRAINT valid_billing_cycle CHECK (billing_cycle IN ('monthly', 'annual')),
    CONSTRAINT valid_status CHECK (status IN ('active', 'trialing', 'paused', 'canceled', 'expired'))
);

CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_entity ON subscriptions(entity_id, entity_type);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_next_billing ON subscriptions(next_billing_date);
```

---

## 12. API Endpoints

### 12.1 Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/areas` | List available areas | No |
| GET | `/api/v1/areas/{id}` | Get area details | No |
| POST | `/api/v1/auth/register` | Register new user | No |
| POST | `/api/v1/auth/login` | User login | No |
| POST | `/api/v1/auth/refresh` | Refresh access token | No |
| POST | `/api/v1/auth/logout` | Logout user | Yes |
| POST | `/api/v1/auth/forgot-password` | Request password reset | No |
| POST | `/api/v1/auth/reset-password` | Reset password | No |
| POST | `/api/v1/auth/verify-email` | Verify email address | No |
| POST | `/api/v1/auth/verify-phone` | Verify phone number | No |
| GET | `/api/v1/auth/oauth/google` | Google OAuth | No |
| GET | `/api/v1/auth/oauth/facebook` | Facebook OAuth | No |

### 12.2 User Management Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/users/me` | Get current user | Yes |
| PUT | `/api/v1/users/me` | Update current user | Yes |
| DELETE | `/api/v1/users/me` | Delete account | Yes |
| POST | `/api/v1/users/me/mfa/enable` | Enable MFA | Yes |
| POST | `/api/v1/users/me/mfa/disable` | Disable MFA | Yes |
| PUT | `/api/v1/users/me/area` | Update user area | Yes |
| POST | `/api/v1/users/me/verify-id` | Verify government ID | Yes |

### 12.3 Payment Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/payments` | List user payments | Yes |
| GET | `/api/v1/payments/{id}` | Get payment details | Yes |
| POST | `/api/v1/payments` | Create payment | Yes |
| POST | `/api/v1/payments/{id}/refund` | Refund payment | Yes |
| GET | `/api/v1/payments/methods` | List payment methods | Yes |
| POST | `/api/v1/payments/methods` | Add payment method | Yes |
| DELETE | `/api/v1/payments/methods/{id}` | Remove payment method | Yes |

### 12.4 Subscription Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/api/v1/subscriptions` | List subscriptions | Yes |
| GET | `/api/v1/subscriptions/{id}` | Get subscription | Yes |
| POST | `/api/v1/subscriptions` | Create subscription | Yes |
| PUT | `/api/v1/subscriptions/{id}/upgrade` | Upgrade subscription | Yes |
| PUT | `/api/v1/subscriptions/{id}/downgrade` | Downgrade subscription | Yes |
| PUT | `/api/v1/subscriptions/{id}/pause` | Pause subscription | Yes |
| PUT | `/api/v1/subscriptions/{id}/resume` | Resume subscription | Yes |
| DELETE | `/api/v1/subscriptions/{id}` | Cancel subscription | Yes |
| GET | `/api/v1/subscriptions/{id}/usage` | Get usage stats | Yes |

---

## 13. Event Definitions

### 13.1 User Events

```go
// packages/events/user_events.go
package events

type UserCreatedEvent struct {
    EventID   string    `json:"event_id"`
    UserID    uuid.UUID `json:"user_id"`
    Email     string    `json:"email"`
    AreaID    uuid.UUID `json:"area_id"`
    CreatedAt time.Time `json:"created_at"`
    EventType string    `json:"event_type"`
    Version   string    `json:"version"`
}

type UserVerifiedEvent struct {
    EventID          string    `json:"event_id"`
    UserID           uuid.UUID `json:"user_id"`
    VerificationType string    `json:"verification_type"` // email, phone, id
    VerificationLevel int      `json:"verification_level"`
    VerifiedAt       time.Time `json:"verified_at"`
    EventType        string    `json:"event_type"`
    Version          string    `json:"version"`
}

type UserDeletedEvent struct {
    EventID   string    `json:"event_id"`
    UserID    uuid.UUID `json:"user_id"`
    DeletedAt time.Time `json:"deleted_at"`
    EventType string    `json:"event_type"`
    Version   string    `json:"version"`
}
```

### 13.2 Payment Events

```go
// packages/events/payment_events.go
package events

type PaymentCompletedEvent struct {
    EventID     string    `json:"event_id"`
    PaymentID   uuid.UUID `json:"payment_id"`
    UserID      uuid.UUID `json:"user_id"`
    Amount      float64   `json:"amount"`
    Currency    string    `json:"currency"`
    Method      string    `json:"method"`
    CompletedAt time.Time `json:"completed_at"`
    EventType   string    `json:"event_type"`
    Version     string    `json:"version"`
}

type PaymentRefundedEvent struct {
    EventID        string    `json:"event_id"`
    PaymentID      uuid.UUID `json:"payment_id"`
    UserID         uuid.UUID `json:"user_id"`
    RefundedAmount float64   `json:"refunded_amount"`
    RefundReason   string    `json:"refund_reason"`
    RefundedAt     time.Time `json:"refunded_at"`
    EventType      string    `json:"event_type"`
    Version        string    `json:"version"`
}
```

### 13.3 Subscription Events

```go
// packages/events/subscription_events.go
package events

type SubscriptionCreatedEvent struct {
    EventID        string    `json:"event_id"`
    SubscriptionID uuid.UUID `json:"subscription_id"`
    UserID         uuid.UUID `json:"user_id"`
    EntityID       uuid.UUID `json:"entity_id"`
    EntityType     string    `json:"entity_type"`
    Tier           string    `json:"tier"`
    CreatedAt      time.Time `json:"created_at"`
    EventType      string    `json:"event_type"`
    Version        string    `json:"version"`
}

type SubscriptionRenewedEvent struct {
    EventID        string    `json:"event_id"`
    SubscriptionID uuid.UUID `json:"subscription_id"`
    UserID         uuid.UUID `json:"user_id"`
    NextBillingDate time.Time `json:"next_billing_date"`
    RenewedAt      time.Time `json:"renewed_at"`
    EventType      string    `json:"event_type"`
    Version        string    `json:"version"`
}
```

---

## 14. Security Implementation

### 14.1 Security Checklist

- [x] SQL Injection Prevention (parameterized queries with sqlx)
- [x] XSS Prevention (output encoding, CSP headers)
- [x] CSRF Protection (token validation)
- [x] Authentication (JWT with refresh tokens)
- [x] Authorization (role-based access control)
- [x] Password Security (bcrypt with cost 12+)
- [x] Rate Limiting (per-IP and per-user)
- [x] Input Validation (go-playground/validator)
- [x] Security Headers (HSTS, X-Frame-Options, etc.)
- [x] Secrets Management (Vault/AWS Secrets Manager)
- [x] Audit Logging (structured logs with slog)
- [x] MFA Support (TOTP)
- [x] Session Management (Redis-backed)
- [x] API Key Management (for service-to-service)

### 14.2 Security Middleware

```go
// internal/adapters/http/middleware/security.go
package middleware

func SecurityHeaders(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        w.Header().Set("X-Content-Type-Options", "nosniff")
        w.Header().Set("X-Frame-Options", "DENY")
        w.Header().Set("X-XSS-Protection", "1; mode=block")
        w.Header().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
        w.Header().Set("Content-Security-Policy", "default-src 'self'")
        w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
        w.Header().Set("Permissions-Policy", "geolocation=(), microphone=(), camera=()")

        next.ServeHTTP(w, r)
    })
}
```

---

## 15. Deployment Configuration

### 15.1 Dockerfile for API

```dockerfile
# Dockerfile.api
FROM golang:1.25-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o api ./cmd/api

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/api .
COPY --from=builder /app/configs ./configs
COPY --from=builder /app/migrations ./migrations

EXPOSE 8080
CMD ["./api"]
```

### 15.2 Dockerfile for Worker

```dockerfile
# Dockerfile.worker
FROM golang:1.25-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o worker ./cmd/worker

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/worker .
COPY --from=builder /app/configs ./configs

CMD ["./worker"]
```

### 15.3 Kubernetes Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: core-api
  namespace: mytorra
spec:
  replicas: 3
  selector:
    matchLabels:
      app: core-api
  template:
    metadata:
      labels:
        app: core-api
    spec:
      containers:
      - name: api
        image: mytorra/core-api:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: core-secrets
              key: db-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: core-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: core-worker
  namespace: mytorra
spec:
  replicas: 2
  selector:
    matchLabels:
      app: core-worker
  template:
    metadata:
      labels:
        app: core-worker
    spec:
      containers:
      - name: worker
        image: mytorra/core-worker:latest
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: core-secrets
              key: redis-password
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
```

---

## Implementation Timeline

### Week 1-2: Foundation & Domain
- Day 1-2: Project setup, dependencies, configuration
- Day 3-5: Domain entities and value objects
- Day 6-7: Application services (user, auth)
- Day 8-10: Application services (payment, subscription, notification)

### Week 3-4: Infrastructure & API
- Day 11-13: PostgreSQL repositories
- Day 14-15: Redis cache and session management
- Day 16-17: RabbitMQ event publishing
- Day 18-19: HTTP handlers and middleware
- Day 20: API documentation

### Week 5: Workers & Integration
- Day 21-22: Background worker setup
- Day 23: Email and SMS handlers
- Day 24: Payment processing handlers
- Day 25: Subscription renewal handlers

### Week 6: Testing & Deployment
- Day 26-27: Unit tests
- Day 28: Integration tests
- Day 29: Load testing
- Day 30: Docker and Kubernetes configuration

---

## Success Criteria

1. **Functionality**
   - All core features implemented and working
   - Background jobs processing reliably
   - Event publishing functioning correctly

2. **Performance**
   - API response time < 200ms for 95th percentile
   - Support 1000+ concurrent users
   - Database queries optimized with proper indexes

3. **Security**
   - All security guidelines implemented
   - No critical vulnerabilities in security scan
   - Proper authentication and authorization

4. **Testing**
   - 80%+ code coverage for business logic
   - All integration tests passing
   - Load tests meeting performance targets

5. **Documentation**
   - OpenAPI specification complete
   - README with setup instructions
   - Architecture decision records documented

---

## Next Steps

1. Review and approve this implementation plan
2. Set up development environment
3. Begin Phase 1: Foundation Setup
4. Daily progress updates in project.todo
5. Weekly review meetings for progress assessment

---

This implementation plan provides a comprehensive roadmap for building the Core Service. Each phase builds upon the previous one, ensuring a solid foundation for the MyTorra platform's most critical service.
