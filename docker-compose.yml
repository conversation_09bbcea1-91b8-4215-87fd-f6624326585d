version: "3.8"

services:
  # PostgreSQL with PostGIS
  postgres:
    image: postgis/postgis:17-3.5-alpine
    container_name: torra-postgres
    environment:
      POSTGRES_USER: torra
      POSTGRES_PASSWORD: torra_dev_password
      POSTGRES_DB: torra_dev
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - torra-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U torra"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and Asynq
  redis:
    image: redis:7-alpine
    container_name: torra-redis
    command: redis-server --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - torra-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ for message bus
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: torra-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: torra
      RABBITMQ_DEFAULT_PASS: torra_dev_password
    ports:
      - "5672:5672" # AMQP port
      - "15672:15672" # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - torra-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Meilisearch for search functionality
  meilisearch:
    image: getmeili/meilisearch:v1.5
    container_name: torra-meilisearch
    environment:
      MEILI_MASTER_KEY: torra_dev_master_key
      MEILI_ENV: development
    ports:
      - "7700:7700"
    volumes:
      - meilisearch_data:/meili_data
    networks:
      - torra-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7700/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Centrifugo for real-time messaging
  centrifugo:
    image: centrifugo/centrifugo:v5
    container_name: torra-centrifugo
    command: centrifugo --config=/centrifugo/config.json --admin
    ports:
      - "8000:8000" # WebSocket/SockJS
      - "8001:8001" # Admin UI
    volumes:
      - ./infra/centrifugo/config.json:/centrifugo/config.json
    networks:
      - torra-network
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:8000/health",
        ]
      interval: 10s
      timeout: 5s
      retries: 5

  # Asynqmon for job monitoring (optional in dev)
  asynqmon:
    image: hibiken/asynqmon:latest
    container_name: torra-asynqmon
    ports:
      - "8080:8080"
    command:
      - "--redis-addr=redis:6379"
    depends_on:
      - redis
    networks:
      - torra-network

  # MinIO for S3-compatible object storage (development)
  minio:
    image: minio/minio:latest
    container_name: torra-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: torra_minio
      MINIO_ROOT_PASSWORD: torra_dev_password
    ports:
      - "9000:9000" # API
      - "9001:9001" # Console
    volumes:
      - minio_data:/data
    networks:
      - torra-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: torra-mailhog
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - torra-network

networks:
  torra-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  meilisearch_data:
  minio_data:
