name: 'Setup Go Environment'
description: 'Setup Go with caching and build tools'
inputs:
  go-version:
    description: 'Go version to use'
    required: true
    default: '1.25'
  install-tools:
    description: 'Whether to install build tools (make, etc.)'
    required: false
    default: 'true'
  cache-key-suffix:
    description: 'Additional suffix for cache key'
    required: false
    default: ''

runs:
  using: 'composite'
  steps:
    - name: Setup Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ inputs.go-version }}
        cache: true
        cache-dependency-path: |
          go.work.sum
          **/go.sum

    - name: Install build tools
      if: inputs.install-tools == 'true'
      shell: bash
      run: |
        sudo apt-get update
        sudo apt-get install -y make

    - name: Cache Go modules
      uses: actions/cache@v4
      with:
        path: |
          ~/go/pkg/mod
          ~/.cache/go-build
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}-${{ inputs.cache-key-suffix }}
        restore-keys: |
          ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          ${{ runner.os }}-go-

    - name: Download dependencies
      shell: bash
      run: |
        go mod download
        go work sync
